import httpx

def execute_code(language: str, code: str, preload: str = "") -> str:
    url = "http://172.30.196.248:8194/v1/sandbox/run"
    headers = {"X-Api-Key": "dify-sandbox"}  # 确保这个 Key 正确

    payload = {
        "language": language,
        "code": code,
        "preload": preload,
        "enable_network": True
    }

    # 可选超时配置（也可以使用默认）
    timeout = httpx.Timeout(60.0)

    # 使用 context 管理器以便自动关闭连接
    with httpx.Client(timeout=timeout) as client:
        response = client.post(url, json=payload, headers=headers)

        # 直接抛出异常（如 403, 500, 503 等）
        response.raise_for_status()

        # 返回响应 JSON 内容
        return response.json()

# 调用示例
if __name__ == "__main__":
    try:
        result = execute_code("python3", "print(8149 * 2)")
        print("执行结果:", result)
    except httpx.HTTPStatusError as http_exc:
        print("服务器返回异常状态码:", http_exc.response.status_code)
        print("响应内容:", http_exc.response.text)
        raise  # 继续抛出
    except httpx.RequestError as req_exc:
        print("请求发送失败:", str(req_exc))
        raise  # 继续抛出
