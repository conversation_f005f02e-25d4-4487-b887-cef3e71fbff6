const translation = {
  common: {
    effectVarConfirm: {
      title: 'Odstrani spremenljivko',
      content: 'Spremenljivka se uporablja v drugih vozliščih. Ali jo še vedno želite odstraniti?',
    },
    parallelTip: {
      click: {
        desc: 'dodati',
        title: '<PERSON><PERSON><PERSON>',
      },
      drag: {
        desc: 'povezati',
        title: '<PERSON><PERSON><PERSON><PERSON>',
      },
      depthLimit: 'Meja paralelnega gnezdenja plasti {{num}} plasti',
      limit: 'Paralelizem je omejen na {{num}} veje.',
    },
    versionHistory: '<PERSON><PERSON><PERSON><PERSON>',
    published: 'Objavljeno',
    run: 'Teči',
    featuresDocLink: 'Nauči se več',
    notRunning: 'Še ne teče',
    exportImage: 'Izvozi sliko',
    openInExplore: 'Odpri v Raziskovanju',
    publishUpdate: 'Obja<PERSON> posodobitev',
    disconnect: 'Odklop',
    exportJPEG: 'Iz<PERSON><PERSON> kot JPEG',
    exportSVG: '<PERSON>zvozi kot SVG',
    model: 'Model',
    restart: 'Znova zaženi',
    running: 'Tek',
    undo: 'Razveljavi',
    enableJinja: 'Omogoči podporo za Jinja predloge',
    publish: 'Objavi',
    importSuccess: 'Uvoz uspešen',
    workflowAsTool: 'Delovni potek kot orodje',
    update: 'Posodobitev',
    jumpToNode: 'Preskoči na ta vozel',
    publishedAt: 'Objavljeno',
    addParallelNode: 'Dodaj paralelni vozlišče',
    inPreview: 'V predogledu',
    workflowAsToolTip: 'Zaradi posodobitve delovnega poteka je potrebna ponovna konfiguracija orodja.',
    variableNamePlaceholder: 'Ime spremenljivke',
    needEndNode: 'Skrivnostna vozlišča je treba dodati.',
    onFailure: 'O neuspehu',
    embedIntoSite: 'Vstavite v spletno stran',
    conversationLog: 'Pogovor Log',
    accessAPIReference: 'Dostop do referenčnega API-ja',
    inPreviewMode: 'V načinu predogleda',
    previewPlaceholder: 'Vnesite vsebino v spodnje polje, da začnete odpravljati napake v chatbotu',
    input: 'Vnos',
    importDSLTip: 'Trenutni osnutek bo prepisan. Izvozite delovni postopek kot varnostno kopijo pred uvozom.',
    duplicate: 'Podvojiti',
    loadMore: 'Naloži več',
    addTitle: 'Dodajte naslov...',
    goBackToEdit: 'Pojdi nazaj k uredniku',
    needAnswerNode: 'Skrivnostni element mora biti dodan.',
    needConnectTip: 'Ta korak ni povezan z ničemer.',
    searchVar: 'Iskalna spremenljivka',
    branch: 'VEJA',
    viewRunHistory: 'Poglej zgodovino izvajanja',
    learnMore: 'Nauči se več',
    workflowProcess: 'Delovni postopek',
    preview: 'Predogled',
    output: 'Izhod',
    viewDetailInTracingPanel: 'Oglejte si podrobnosti',
    debugAndPreview: 'Predogled',
    restore: 'Obnovi',
    latestPublished: 'Najnovejša objavljena',
    importDSL: 'Uvozi DSL',
    viewOnly: 'Samo za ogled',
    insertVarTip: 'Pritisnite tipko \'/\' za hitro vstavljanje',
    currentDraftUnpublished: 'Trenutna osnutek neobjavljeno',
    showRunHistory: 'Prikaži zgodovino izvajanja',
    runHistory: 'Zgodovina izvajanja',
    fileUploadTip: 'Funkcije nalaganja slik so bile nadgrajene na nalaganje datotek.',
    backupCurrentDraft: 'Varnostno kopiraj trenutni osnutek',
    overwriteAndImport: 'Prepiši in uvozi',
    features: 'Značilnosti',
    exportPNG: 'Izvozi kot PNG',
    parallelRun: 'Paralelni tek',
    chooseDSL: 'Izberi DSL datoteko',
    unpublished: 'Nepublikirano',
    pasteHere: 'Prilepite tukaj',
    featuresDescription: 'Izboljšanje uporabniške izkušnje spletne aplikacije',
    exitVersions: 'Izhodne različice',
    editing: 'Urejanje',
    addFailureBranch: 'Dodaj neuspešno vejo',
    syncingData: 'Sinhronizacija podatkov, samo nekaj sekund.',
    noVar: 'Brez spremenljivke',
    runApp: 'Zaženi aplikacijo',
    ImageUploadLegacyTip: 'Zdaj lahko v začetni obliki ustvarite spremenljivke datotečnega tipa. V prihodnje ne bomo več podpirali funkcije nalaganja slik.',
    importWarning: 'Opozorilo',
    copy: 'Kopirati',
    redo: 'Ponovno naredi',
    currentDraft: 'Trenutni osnutek',
    manageInTools: 'Upravljajte v orodjih',
    parallel: 'PARALELNO',
    importWarningDetails: 'Razlika v različici DSL lahko vpliva na nekatere funkcije',
    addDescription: 'Dodajte opis...',
    maxTreeDepth: 'Največje število {{depth}} vozlišč na vejo',
    jinjaEditorPlaceholder: 'Vnesite \'/\' ali \'{\', da vstavite spremenljivko',
    batchRunApp: 'Program za serijsko izvajanje',
    importFailure: 'Uvoz ni uspel',
    handMode: 'Ročni način',
    processData: 'Obdelava podatkov',
    addBlock: 'Dodaj vozlišče',
    noHistory: 'Brez zgodovine',
    configureRequired: 'Konfigurirajte zahteve',
    setVarValuePlaceholder: 'Nastavi spremenljivko',
    pointerMode: 'Način s kazalcem',
    autoSaved: 'Samodejno shranjeno',
    configure: 'Konfiguriraj',
    inRunMode: 'V načinu izvajanja',
  },
  env: {
    modal: {
      value: 'Vrednost',
      title: 'Dodaj okoljsko spremenljivko',
      name: 'Ime',
      valuePlaceholder: 'vrednost okolja',
      namePlaceholder: 'ime okolja',
      type: 'Tip',
      editTitle: 'Uredi okoljsko spremenljivko',
      secretTip: 'Uporablja se za opredelitev občutljivih informacij ali podatkov, s konfiguriranimi nastavitvami DSL za preprečevanje puščanja.',
    },
    export: {
      export: 'Izvozi DSL z skrivnimi vrednostmi',
      ignore: 'Izvoz DSL',
      checkbox: 'Izvozi tajne vrednosti',
      title: 'Izvozi skrivne okoljske spremenljivke?',
    },
    envPanelTitle: 'Spremenljivke okolja',
    envPanelButton: 'Dodaj spremenljivko',
    envDescription: 'Okoljske spremenljivke se lahko uporabljajo za shranjevanje zasebnih informacij in poverilnic. So samo za branje in jih je mogoče ločiti od DSL datoteke med izvozem.',
  },
  chatVariable: {
    modal: {
      namePlaceholder: 'Ime spremenljivke',
      title: 'Dodaj spremenljivko za pogovor',
      editInJSON: 'Uredi v JSON',
      objectKey: 'Ključ',
      valuePlaceholder: 'Privzeta vrednost, pustite prazno, da je ne nastavite',
      description: 'Opis',
      type: 'Tip',
      value: 'Privzeta vrednost',
      name: 'Ime',
      arrayValue: 'Vrednost',
      editTitle: 'Uredi spremenljivko pogovora',
      editInForm: 'Uredi v obrazcu',
      addArrayValue: 'Dodati vrednost',
      objectType: 'Tip',
      oneByOne: 'Dodaj eno po eno',
      objectValue: 'Privzeta vrednost',
      descriptionPlaceholder: 'Opisujte spremenljivko',
    },
    updatedAt: 'Posodobljeno ob',
    docLink: 'Obiščite našo dokumentacijo, da se naučite več.',
    panelTitle: 'Pogovor Spremenljivke',
    storedContent: 'Shranjena vsebina',
    panelDescription: 'Spremenljivke pogovora se uporabljajo za shranjevanje interaktivnih informacij, ki se jih LLM mora zapomniti, vključno z zgodovino pogovorov, naloženimi datotekami in uporabnikovimi nastavitvami. So branje-in-pisanje.',
    button: 'Dodaj spremenljivko',
  },
  changeHistory: {
    stepBackward_other: '{{count}} korakov nazaj',
    sessionStart: 'Začetek seje',
    nodeTitleChange: 'Naslov vozlišča je bil spremenjen',
    noteChange: 'Opomba spremenjena',
    title: 'Zgodovina sprememb',
    noteAdd: 'Opomba dodana',
    nodeAdd: 'Vozlišče dodano',
    nodeDragStop: 'Vozlišče je bilo premaknjeno',
    stepBackward_one: '{{count}} korak nazaj',
    stepForward_other: '{{count}} korakov naprej',
    nodeDelete: 'Vozlišče je bilo izbrisano',
    edgeDelete: 'Vozlišče je odklopljeno',
    nodeResize: 'Vozlišče je spremenjeno velikost',
    hint: 'Namig',
    nodeDescriptionChange: 'Opis vozlišča je bil spremenjen',
    noteDelete: 'Opomba izbrisana',
    currentState: 'Trenutno stanje',
    nodeConnect: 'Povezana vozlišča',
    nodeChange: 'Vozlišče se je spremenilo',
    nodePaste: 'Vozlišče prilepljeno',
    clearHistory: 'Počisti zgodovino',
    hintText: 'Vaša dejanja urejanja so sledena v zgodovini sprememb, ki se hrani na vaši napravi za čas trajanja te seje. Ta zgodovina bo izbrisana, ko zapustite urejevalnik.',
    placeholder: 'Še niste spremenili ničesar.',
    stepForward_one: '{{count}} korak naprej',
  },
  errorMsg: {
    fields: {
      variableValue: 'Spremenljivka Vrednost',
      model: 'Model',
      variable: 'Spremenljivka Ime',
      code: 'Koda',
      rerankModel: 'Konfiguriran model ponovne uvrstitve',
      visionVariable: 'Vizijska spremenljivka',
    },
    invalidVariable: 'Neveljavna spremenljivka',
    noValidTool: '{{field}} ni izbranega veljavnega orodja',
    toolParameterRequired: '{{field}}: parameter [{{param}}] je obvezen',
    rerankModelRequired: 'Zahteva se konfigurirana model ponovnega razvrščanja.',
    authRequired: 'Zahtevana je avtorizacija',
    invalidJson: '{{field}} je neveljaven JSON',
    fieldRequired: '{{field}} je obvezno',
  },
  singleRun: {
    iteration: 'Iteracija',
    startRun: 'Začni zagon',
    loop: 'Zanka',
    running: 'Tek',
    testRunIteration: 'Testiranje ponovitve',
    back: 'Nazaj',
    testRun: 'Testna vožnja',
  },
  tabs: {
    'customTool': 'Po meri',
    'logic': 'Logika',
    'tools': 'Orodja',
    'searchBlock': 'Išči vozlišče',
    'utilities': 'Komunalne storitve',
    'plugin': 'Vtičnik',
    'allTool': 'Vse',
    'searchTool': 'Orodje za iskanje',
    'workflowTool': 'Delovni tok',
    'noResult': 'Ni bilo najdenih ujemanj',
    'transform': 'Pretvori',
    'blocks': 'Vozlišča',
    'question-understand': 'Vprašanje Razumevanje',
    'agent': 'Agentska strategija',
  },
  blocks: {
    'iteration': 'Iteracija',
    'if-else': 'Če/Drugače',
    'llm': 'LLM',
    'document-extractor': 'Ekstraktor dokumentov',
    'knowledge-retrieval': 'Pridobivanje znanja',
    'loop-start': 'Začetek zanke',
    'assigner': 'Dodeljevalec spremenljivk',
    'question-classifier': 'Razvrščevalec vprašanj',
    'start': 'Začni',
    'loop-end': 'Izhod iz zanke',
    'http-request': 'HTTP zahteva',
    'code': 'Koda',
    'template-transform': 'Predloga',
    'answer': 'Odgovor',
    'end': 'Konec',
    'iteration-start': 'Začetek iteracije',
    'list-operator': 'Seznam operater',
    'variable-aggregator': 'Spremenljivka agregator',
    'parameter-extractor': 'Ekstraktor parametrov',
    'loop': 'Zanka',
    'agent': 'Agent',
    'variable-assigner': 'Spremenljivka agregator',
  },
  blocksAbout: {
    'list-operator': 'Uporabljeno za filtriranje ali razvrščanje vsebine polja.',
    'template-transform': 'Pretvori podatke v niz z uporabo Jinja predloge',
    'if-else': 'Omogoča vam, da razdelite delovni tok na dve veji na podlagi pogojev if/else.',
    'code': 'Izvedite kos Python ali NodeJS kode za izvajanje prilagojene logike.',
    'iteration': 'Izvedite več korakov na seznamu objektov, dokler niso vsi rezultati izpisani.',
    'loop-end': 'Enakovredno „prekini“. Ta vozlišče nima konfiguracijskih elementov. Ko telo zanke doseže to vozlišče, zanka preneha.',
    'document-extractor': 'Uporabljeno za razčlenitev prenesenih dokumentov v besedilno vsebino, ki jo je enostavno razumeti za LLM.',
    'answer': 'Določi vsebino odgovora v pogovoru.',
    'end': 'Določite tip konca in rezultata delovnega toka',
    'knowledge-retrieval': 'Omogoča vam, da poizvedujete o besedilnih vsebinah, povezanih z vprašanji uporabnikov iz znanja.',
    'http-request': 'Dovoli pošiljanje zahtevkov strežniku prek protokola HTTP',
    'llm': 'Uporaba velikih jezikovnih modelov za odgovarjanje na vprašanja ali obdelavo naravnega jezika',
    'loop': 'Izvedite zanko logike, dokler ni izpolnjen pogoj za prekinitev ali dokler ni dosežena največja število ponovitev.',
    'question-classifier': 'Določite pogoje klasifikacije uporabniških vprašanj, LLM lahko določi, kako se pogovor razvija na podlagi opisa klasifikacije.',
    'parameter-extractor': 'Uporabite LLM za pridobivanje strukturiranih parametrov iz naravnega jezika za klice orodij ali HTTP zahtev.',
    'agent': 'Uporaba velikih jezikovnih modelov za odgovarjanje na vprašanja ali obdelavo naravnega jezika',
    'start': 'Določite začetne parametre za zagon delovnega toka',
    'variable-assigner': 'Združite večpodružinske spremenljivke v eno samo spremenljivko za enotno konfiguracijo spodnjih vozlišč.',
    'variable-aggregator': 'Združite večpodružnične spremenljivke v eno samo spremenljivko za enotno konfiguracijo spodnjih vozlišč.',
  },
  operator: {
    zoomOut: 'Zoomirati ven',
    zoomToFit: 'Povečaj, da se prilega',
    zoomIn: 'Zoom in',
    zoomTo50: 'Povečaj na 50%',
    zoomTo100: 'Povečaj na 100%',
  },
  variableReference: {
    conversationVars: 'pogovorne spremenljivke',
    assignedVarsDescription: 'Dodeljene spremenljivke morajo biti spremenljivke, ki jih je mogoče pisati, na primer',
    noAvailableVars: 'Ni razpoložljivih spremenljivk.',
    noAssignedVars: 'Nobenih dodeljenih spremenljivk ni na voljo.',
    noVarsForOperation: 'Za izbrano operacijo ni nobenih spremenljivk, ki bi jih bilo mogoče dodeliti.',
  },
  panel: {
    change: 'Spremeni',
    about: 'O tem',
    userInputField: 'Uporabniško vhodno polje',
    nextStep: 'Naslednji korak',
    runThisStep: 'Izvedi ta korak',
    changeBlock: 'Spremeni vozlišče',
    addNextStep: 'Dodajte naslednji korak v ta delovni potek',
    moveToThisNode: 'Premakni se na to vozlišče',
    checklistTip: 'Prepričajte se, da so vse težave rešene, preden objavite.',
    selectNextStep: 'Izberi naslednji korak',
    helpLink: 'Pomočna povezava',
    checklist: 'Kontrolni seznam',
    checklistResolved: 'Vse težave so rešene',
    createdBy: 'Ustvarjeno z',
    organizeBlocks: 'Organizirajte vozlišča',
    minimize: 'Izhod iz celotnega zaslona',
    maximize: 'Maksimiziraj platno',
  },
  nodes: {
    common: {
      memory: {
        user: 'Uporabniški predpon',
        assistant: 'Pomagalec predpona',
        memory: 'Spomin',
        conversationRoleName: 'Ime vloge v pogovoru',
        memoryTip: 'Nastavitve spomina za klepet',
        windowSize: 'Velikost okna',
      },
      memories: {
        tip: 'Pomnilnik klepeta',
        title: 'Spomini',
        builtIn: 'Vgrajeno',
      },
      errorHandle: {
        none: {
          title: 'Noben',
          desc: 'Vozlišče se bo prenehalo izvajati, če pride do izjeme, ki ni obravnavana.',
        },
        defaultValue: {
          output: 'Privzeta vrednost izhoda',
          inLog: 'Napaka vozlišča, izhod po privzetih vrednostih.',
          title: 'Privzeta vrednost',
          desc: 'Ko pride do napake, določi statično vsebino izhoda.',
          tip: 'Ob napaki bo vrnil spodnjo vrednost.',
        },
        failBranch: {
          title: 'Napaka veja',
          customize: 'Pojdite na platno, da prilagodite logiko veje neuspeha.',
          desc: 'Ko pride do napake, se bo izvedla veja izjeme.',
          inLog: 'Napaka na vozlišču, samodejno se bo izvedla veja za neuspeh. Izhod vozlišča bo vrnil tip napake in sporočilo o napaki ter ju posredoval naprej.',
          customizeTip: 'Ko je aktivirana veja napak, izjeme, ki jih sprožijo vozlišča, ne bodo prekinile procesa. Namesto tega bo samodejno izvedena vnaprej določena veja napak, kar vam omogoča, da prilagodljivo ponudite sporočila o napakah, poročila, popravke ali preskočite dejanja.',
        },
        partialSucceeded: {
          tip: 'V procesu je {{num}} vozlišč, ki delujejo nenormalno, prosim, pojdite na sledenje, da preverite dnevnike.',
        },
        title: 'Obvladovanje napak',
        tip: 'Strategija ravnanja z izjemo, ki se sproži, ko vozlišče naleti na izjemo.',
      },
      retry: {
        retrySuccessful: 'Ponovni poskus je bil uspešen',
        retryFailedTimes: '{{times}} poskusi so spodleteli',
        maxRetries: 'maksimalno število poskusov',
        ms: 'ms',
        retrying: 'Ponovno poskušam...',
        times: 'časi',
        retry: 'Poskusi znova',
        retryFailed: 'Ponovi neuspeh',
        retryOnFailure: 'poskusi znova v primeru napake',
        retryInterval: 'ponovni interval',
        retries: '{{num}} Poskusi',
        retryTimes: 'Poskusite {{times}} krat v primeru napake',
      },
      insertVarTip: 'Vstavite spremenljivko',
      outputVars: 'Izhodne spremenljivke',
    },
    start: {
      outputVars: {
        memories: {
          des: 'Zgodovina pogovora',
          type: 'vrsta sporočila',
          content: 'vsebina sporočila',
        },
        files: 'Seznam datotek',
        query: 'Uporabniški vnos',
      },
      noVarTip: 'Nastavite vhodne podatke, ki jih lahko uporabite v delovnem toku.',
      required: 'zahtevano',
      builtInVar: 'Vgrajene spremenljivke',
      inputField: 'Vnosno polje',
    },
    end: {
      output: {
        type: 'izhodna vrsta',
        variable: 'izhodna spremenljivka',
      },
      type: {
        'structured': 'Strukturirano',
        'none': 'Noben',
        'plain-text': 'Navadno besedilo',
      },
      outputs: 'Izhodi',
    },
    answer: {
      outputVars: 'Izhodne spremenljivke',
      answer: 'Odgovor',
    },
    llm: {
      roleDescription: {
        system: 'Dajte visoko raven navodil za pogovor.',
        assistant: 'Odgovori modela so zasnovani na uporabnikovih sporočilih.',
        user: 'Navedite navodila, poizvedbe ali kakršnokoli besedilne vnose modelu',
      },
      resolution: {
        low: 'Nizko',
        high: 'Visoko',
        name: 'Resolucija',
      },
      outputVars: {
        output: 'Ustvari vsebino',
        usage: 'Informacije o uporabi modela',
      },
      singleRun: {
        variable: 'Spremenljivka',
      },
      jsonSchema: {
        warningTips: {
          saveSchema: 'Prosimo, da dokončate urejanje trenutnega polja, preden shranite shemo.',
        },
        generatedResult: 'Generiran rezultat',
        instruction: 'Navodilo',
        resetDefaults: 'Ponastavi',
        promptPlaceholder: 'Opiši svoj JSON shemo...',
        generating: 'Generiranje JSON sheme...',
        resultTip: 'Tukaj je generiran rezultat. Če niste zadovoljni, se lahko vrnete in spremenite svoj poziv.',
        promptTooltip: 'Pretvorite besedilni opis v standardizirano strukturo JSON sheme.',
        addField: 'Dodaj polje',
        fieldNamePlaceholder: 'Ime polja',
        import: 'Uvoz iz JSON',
        generationTip: 'Lahko uporabite naravni jezik za hitro ustvarjanje JSON sheme.',
        back: 'Nazaj',
        descriptionPlaceholder: 'Dodajte opis',
        generate: 'Generirati',
        doc: 'Izvedite več o strukturiranem izhodu',
        title: 'Strukturirana izhodna shema',
        required: 'zahtevano',
        apply: 'Uporabi',
        generateJsonSchema: 'Generiraj JSON shemo',
        addChildField: 'Dodaj polje za otroka',
        showAdvancedOptions: 'Prikaži napredne možnosti',
        stringValidations: 'Preverjanje nizov',
        regenerate: 'Ponovno generiranje',
      },
      prompt: 'ukaz',
      sysQueryInUser: 'vprašanje v uporabniškem sporočilu je obvezno',
      notSetContextInPromptTip: 'Da omogočite funkcijo konteksta, prosimo izpolnite spremenljivko konteksta v PROMPT.',
      contextTooltip: 'Lahko uvažate znanje kot kontekst',
      variables: 'spremenljivke',
      files: 'Datoteke',
      model: 'model',
      context: 'kontekst',
      addMessage: 'Dodaj sporočilo',
      vision: 'vizija',
    },
    knowledgeRetrieval: {
      outputVars: {
        title: 'Segmentirana naslov',
        url: 'Segmentirana URL',
        icon: 'Segmentirana ikona',
        content: 'Segmentirana vsebina',
        metadata: 'Drug metapodatki',
        output: 'Podatki o segmentaciji iskanja',
      },
      metadata: {
        options: {
          disabled: {
            title: 'Onemogočeno',
            subTitle: 'Ne omogočanje filtriranja metapodatkov',
          },
          automatic: {
            title: 'Samodejno',
            subTitle: 'Samodejno ustvarite filtrirne pogoje za metapodatke na podlagi uporabniškega poizvedovanja.',
            desc: 'Samodejno ustvarite filtrirne pogoje za metapodatke na podlagi spremenljivke poizvedbe',
          },
          manual: {
            subTitle: 'Ročno dodajte pogoje za filtriranje metapodatkov',
            title: 'Ročno',
          },
        },
        panel: {
          title: 'Pogoji za filtriranje metapodatkov',
          conditions: 'Pogoji',
          placeholder: 'Vnesite vrednost',
          search: 'Išči metapodatke',
          select: 'Izberi spremenljivko...',
          datePlaceholder: 'Izberi čas...',
          add: 'Dodaj pogoj',
        },
        title: 'Filtriranje metapodatkov',
      },
      queryVariable: 'Vprašanje spremenljivka',
      knowledge: 'Znanje',
    },
    http: {
      outputVars: {
        files: 'Seznam datotek',
        body: 'Vsebina odziva',
        headers: 'Seznam odzivnih glav JSON',
        statusCode: 'Statusna koda odgovora',
      },
      authorization: {
        'no-auth': 'Noben',
        'custom': 'Po meri',
        'header': 'Naslov',
        'bearer': 'Nosilac',
        'api-key-title': 'API ključ',
        'authorization': 'Avtorizacija',
        'api-key': 'API-kljuc',
        'basic': 'Osnovno',
        'auth-type': 'Vrsta avtentikacije',
        'authorizationType': 'Vrsta pooblastila',
      },
      timeout: {
        readLabel: 'Časovna omejitev branja',
        title: 'Časovna omejitev',
        readPlaceholder: 'Vnesite časovne omejitve za branje v sekundah',
        connectPlaceholder: 'Vnesite časovne omejitve povezave v sekundah',
        connectLabel: 'Čas povezave je potekel',
        writePlaceholder: 'Vnesite časovne omejitve za pisanje v sekundah',
        writeLabel: 'Časovna omejitev pisanja',
      },
      curl: {
        title: 'Uvozi iz cURL',
        placeholder: 'Prilepite cURL niz tukaj',
      },
      body: 'Telo',
      inputVars: 'Vhodne spremenljivke',
      apiPlaceholder: 'Vnesite URL, vtipkajte \'/\' in vstavite spremenljivko.',
      api: 'API',
      extractListPlaceholder: 'Vnesite indeks seznamske postavke, vnesite \'/\' za vstavitev spremenljivke',
      key: 'Ključ',
      binaryFileVariable: 'Dvojiška datoteka spremenljivka',
      notStartWithHttp: 'API se mora začeti z http:// ali https://',
      keyValueEdit: 'Urejanje ključ-vrednost',
      bulkEdit: 'Masovno urejanje',
      type: 'Tip',
      headers: 'Naslovi',
      value: 'Vrednost',
      params: 'Parametri',
      insertVarPlaceholder: 'vnesite \'/\' za vstavljanje spremenljivke',
    },
    code: {
      searchDependencies: 'Išči odvisnosti',
      advancedDependencies: 'Napredne odvisnosti',
      outputVars: 'Izhodne spremenljivke',
      inputVars: 'Vhodne spremenljivke',
      advancedDependenciesTip: 'Dodajte nekaj vnaprej naloženih odvisnosti, ki potrebujejo več časa za obdelavo ali niso privzete vgrajene.',
    },
    templateTransform: {
      outputVars: {
        output: 'Transformirana vsebina',
      },
      codeSupportTip: 'Podpira samo Jinja2',
      code: 'Koda',
      inputVars: 'Vhodne spremenljivke',
    },
    ifElse: {
      comparisonOperator: {
        'all of': 'vse od',
        'not in': 'ni v',
        'in': 'v',
        'null': 'je nič',
        'after': 'po',
        'is': 'je',
        'not exists': 'ne obstaja',
        'empty': 'je prazno',
        'is not': 'ni',
        'start with': 'začeti z',
        'not empty': 'ni prazen',
        'before': 'pred',
        'end with': 'končati z',
        'not contains': 'ne vsebuje',
        'contains': 'vsebuje',
        'exists': 'obstaja',
        'not null': 'ni ničelno',
      },
      optionName: {
        localUpload: 'Lokalno nalaganje',
        video: 'Video',
        url: 'URL',
        image: 'Slika',
        doc: 'Dokument',
        audio: 'Zvočni zapis',
      },
      addCondition: 'Dodaj pogoj',
      selectVariable: 'Izberi spremenljivko...',
      or: 'ali',
      if: 'Če',
      and: 'in',
      else: 'Drugje',
      notSetVariable: 'Prosim, najprej nastavite spremenljivko.',
      enterValue: 'Vnesite vrednost',
      elseDescription: 'Uporabljeno za opredelitev logike, ki se izvede, ko pogoj if ni izpolnjen.',
      addSubVariable: 'Podspremenljivka',
      conditionNotSetup: 'Pogoji NISO nastavljeni',
      operator: 'Operater',
      select: 'Izberite',
    },
    variableAssigner: {
      type: {
        object: 'Predmet',
        string: 'Niz',
        number: 'Število',
        array: 'Množica',
      },
      outputVars: {
        varDescribe: '{{groupName}} izhod',
      },
      varNotSet: 'Spremenljivka ni nastavljena',
      title: 'Dodelite spremenljivke',
      noVarTip: 'Dodajte spremenljivke, ki jih je treba dodeliti.',
      aggregationGroup: 'Agregacijska skupina',
      outputType: 'Vrsta izhoda',
      addGroup: 'Dodaj skupino',
      setAssignVariable: 'Določite spremenljivko',
      aggregationGroupTip: 'Omogočanje te funkcije omogoča agregatorju spremenljivk, da združi več skupin spremenljivk.',
    },
    assigner: {
      'operations': {
        'set': 'Nabor',
        'append': 'Dodaj',
        '/=': '/=',
        'over-write': 'Prepiši',
        '*=': '*=',
        'remove-first': 'Odstrani prvi',
        'remove-last': 'Odstrani zadnje',
        '-=': '-=',
        '+=': '+=',
        'extend': 'Razširi',
        'clear': 'Jasno',
        'overwrite': 'Prepiši',
        'title': 'Operacija',
      },
      'clear': 'Jasno',
      'plus': 'Plus',
      'noAssignedVars': 'Nobenih dodeljenih spremenljivk ni na voljo.',
      'variables': 'Spremenljivke',
      'assignedVariable': 'Dodeljena spremenljivka',
      'writeMode': 'Načrtovanje pisanja',
      'setParameter': 'Nastavite parameter...',
      'writeModeTip': 'Način dodajanja: Na voljo samo za spremenljivke tipa tabel.',
      'over-write': 'Prepiši',
      'append': 'Dodaj',
      'varNotSet': 'Spremenljivka NI nastavljena',
      'noVarTip': 'Kliknite na gumb " + " za dodajanje spremenljivk',
      'variable': 'Spremenljivka',
      'assignedVarsDescription': 'Dodeljene spremenljivke morajo biti spremenljivke, ki jih je mogoče pisati, kot so spremenljivke za pogovor.',
      'setVariable': 'Nastavi spremenljivko',
      'selectAssignedVariable': 'Izberite dodeljeno spremenljivko...',
    },
    tool: {
      outputVars: {
        files: {
          transfer_method: 'Metoda prenosa. Vrednost je remote_url ali local_file.',
          title: 'orodja ustvarjena datoteke',
          upload_file_id: 'Naložite ID datoteke',
          url: 'URL slike',
          type: 'Vrsta podpore. Zdaj podpiramo samo slike.',
        },
        text: 'vsebina, ki jo je generiral orodje',
        json: 'orodje je ustvarilo json',
      },
      inputVars: 'Vhodne spremenljivke',
      authorize: 'Pooblasti',
    },
    questionClassifiers: {
      outputVars: {
        className: 'Ime razreda',
      },
      instruction: 'Navodilo',
      addClass: 'Dodaj razred',
      class: 'Razred',
      model: 'model',
      topicPlaceholder: 'Napišite ime svoje teme',
      topicName: 'Ime teme',
      instructionTip: 'Vnesite dodatna navodila, ki bodo pomagala klasifikatorju vprašanj bolje razumeti, kako kategorizirati vprašanja.',
      inputVars: 'Vhodne spremenljivke',
      classNamePlaceholder: 'Napiši ime svoje razredi',
      advancedSetting: 'Napredno nastavitev',
      instructionPlaceholder: 'Napišite svoje navodilo',
    },
    parameterExtractor: {
      addExtractParameterContent: {
        required: 'Zahtevano',
        description: 'Opis',
        name: 'Ime',
        descriptionPlaceholder: 'Izvleči opis parametra',
        namePlaceholder: 'Izvleči ime parametra',
        type: 'Tip',
        typePlaceholder: 'Izvleči vrsto parametra',
        requiredContent: 'Zahtevano se uporablja le kot referenca za sklepanje modela in ne kot obvezno validacijo izhodnih parametrov.',
      },
      extractParameters: 'Izvleči parametre',
      errorReason: 'Razlog za napako',
      instruction: 'Navodilo',
      instructionTip: 'Vnesite dodatna navodila, da pomagate izvleku parametrov razumeti, kako izvleči parametre.',
      reasoningMode: 'Način razmišljanja',
      isSuccess: 'Ali je uspeh. Na uspehu je vrednost 1, na neuspehu je vrednost 0.',
      importFromTool: 'Uvoz iz orodij',
      advancedSetting: 'Napredno nastavitev',
      addExtractParameter: 'Dodaj parameter za ekstrakcijo',
      extractParametersNotSet: 'Parameterji za ekstrakcijo niso nastavljeni',
      inputVar: 'Vhodna spremenljivka',
      reasoningModeTip: 'Lahko izberete ustrezen način razmišljanja glede na sposobnost modela, da se odzove na navodila za klic funkcij ali pozive.',
    },
    iteration: {
      ErrorMethod: {
        operationTerminated: 'Prekinjeno',
        removeAbnormalOutput: 'Odstrani nenavadne izhode',
        continueOnError: 'Nadaljuj naprej kljub napaki',
      },
      errorResponseMethod: 'Metoda odziva napake',
      parallelModeEnableTitle: 'Paralelni način vključen',
      output: 'Izhodne spremenljivke',
      MaxParallelismTitle: 'Maksimalno paralelizem',
      currentIteration: 'Trenutna iteracija',
      error_other: '{{count}} Napak',
      comma: ',',
      iteration_one: '{{count}} Iteracija',
      parallelMode: 'Paralelni način',
      error_one: '{{count}} Napaka',
      deleteTitle: 'Izbriši vozlišče ponovitve?',
      iteration_other: '{{count}} ponovitev',
      input: 'Vnos',
      answerNodeWarningDesc: 'Opozorilo o paralelnem načinu: Odgovorni vozli, dodelitve spremenljivk v pogovorih in trajne operacije branja/pisanja znotraj iteracij lahko povzročijo izjeme.',
      parallelModeUpper: 'PARALELNO MODE',
      MaxParallelismDesc: 'Največje paralelizem se uporablja za nadzorovanje števila nalog, ki se izvajajo hkrati v eni iteraciji.',
      deleteDesc: 'Izbris iteracijskega vozlišča bo izbrisal vsa otroška vozlišča.',
      parallelModeEnableDesc: 'V vzporednem načinu naloge znotraj iteracij podpirajo vzporedno izvajanje. To lahko nastavite v razdelku lastnosti na desni strani.',
      parallelPanelDesc: 'V paralelnem načinu naloge v iteraciji podpirajo parallelno izvajanje.',
    },
    loop: {
      ErrorMethod: {
        removeAbnormalOutput: 'Odstrani nenavadne izhode',
        operationTerminated: 'Prekinjeno',
        continueOnError: 'Nadaljuj naprej kljub napaki',
      },
      loop_one: '{{count}} Zanka',
      loop_other: '{{count}} Zavoji',
      input: 'Vnos',
      errorResponseMethod: 'Metoda odziva napake',
      output: 'Izhodna spremenljivka',
      loopMaxCount: 'Maksimalno število zank',
      loopVariables: 'Zanke Spremenljivke',
      comma: ',',
      currentLoop: 'Trenutni obrat',
      currentLoopCount: 'Trenutno število zank: {{count}}',
      deleteTitle: 'Izbriši vozlišče zanke?',
      loopNode: 'Ciklični vozlišče',
      inputMode: 'Vnosni način',
      variableName: 'Spremenljivka Ime',
      exitConditionTip: 'Vozić potrebuje vsaj eno izhodno pogoj.',
      finalLoopVariables: 'Končne zanke spremenljivke',
      deleteDesc: 'Izbris vozlišča zanke bo odstranil vse otroške vozlišča.',
      breakCondition: 'Pogoji za prekinitev zanke',
      error_one: '{{count}} Napaka',
      error_other: '{{count}} Napak',
      setLoopVariables: 'Nastavite spremenljivke znotraj obsega zanke',
      totalLoopCount: 'Skupno število zank: {{count}}',
      initialLoopVariables: 'Začetne spremenljivke zanke',
      breakConditionTip: 'Lahko se sklicujete le na spremenljivke znotraj zank z zaključnimi pogoji in pogovorne spremenljivke.',
      loopMaxCountError: 'Prosimo, vnesite veljavno največje število ponovitev, ki mora biti med 1 in {{maxCount}}',
    },
    note: {
      editor: {
        bold: 'Poudarjeno',
        medium: 'Srednje',
        large: 'Velik',
        link: 'Povezava',
        enterUrl: 'Vnesite URL...',
        small: 'Majhen',
        bulletList: 'Seznam s puščicami',
        unlink: 'Odstrani povezavo',
        italic: 'Italika',
        placeholder: 'Napiši svojo opombo...',
        openLink: 'Odprto',
        showAuthor: 'Prikaži avtorja',
        strikethrough: 'Prečrtano',
        invalidUrl: 'Nesprejemljiv URL',
      },
      addNote: 'Dodaj opombo',
    },
    docExtractor: {
      outputVars: {
        text: 'Izvlečene besedilo',
      },
      supportFileTypes: 'Podpora za vrste datotek: {{types}}.',
      learnMore: 'Nauči se več',
      inputVar: 'Vhodna spremenljivka',
    },
    listFilter: {
      outputVars: {
        first_record: 'Prvi zapis',
        last_record: 'Zadnji zapis',
        result: 'Filtriraj rezultat',
      },
      filterConditionKey: 'Ključ pogoja filtra',
      asc: 'ASC',
      filterConditionComparisonOperator: 'Operator za primerjavo filtrovanja pogojev',
      selectVariableKeyPlaceholder: 'Izberi podključ spremenljivke',
      limit: 'Najboljši N',
      filterConditionComparisonValue: 'Vrednost pogoja filtra',
      desc: 'DESC',
      inputVar: 'Vhodna spremenljivka',
      orderBy: 'Naroči po',
      extractsCondition: 'Izvleči N predmet',
      filterCondition: 'Filtrirni pogoj',
    },
    agent: {
      strategy: {
        configureTip: 'Prosimo, konfigurirajte agentno strategijo.',
        selectTip: 'Izberite agencijsko strategijo',
        searchPlaceholder: 'Išči agentno strategijo',
        label: 'Agentična strategija',
        shortLabel: 'Strategija',
        configureTipDesc: 'Po nastavitvi agentne strategije bo ta vozlišče samodejno naložilo preostale nastavitve. Strategija bo vplivala na mehanizem večstopenjskega razmišljanja o orodju.',
        tooltip: 'Različne agentne strategije določajo, kako sistem načrtuje in izvaja večstopenjske klice orodij.',
      },
      pluginInstaller: {
        installing: 'Namestitev',
        install: 'Namestite',
      },
      modelNotInMarketplace: {
        title: 'Model ni nameščen',
        manageInPlugins: 'Upravljanje v vtičnikih',
        desc: 'Ta model je nameščen iz lokalnega ali GitHub repozitorija. Prosimo, uporabite ga po namestitvi.',
      },
      modelNotSupport: {
        title: 'Nepodprti model',
        desc: 'V različici vtičnika, ki je nameščena, ta model ni na voljo.',
        descForVersionSwitch: 'Nameščena različica vtičnika ne podpira tega modela. Kliknite za preklop na drugo različico.',
      },
      modelSelectorTooltips: {
        deprecated: 'Ta model je zastarelo',
      },
      outputVars: {
        files: {
          type: 'Vrsta podpore. Zdaj podpiramo samo slike.',
          upload_file_id: 'Naložite ID datoteke',
          title: 'datoteke, ki jih je ustvaril agent',
          url: 'URL slike',
          transfer_method: 'Metoda prenosa. Vrednost je remote_url ali local_file.',
        },
        json: 'agent generiran json',
        text: 'vsebina, ki jo je ustvaril agent',
      },
      checkList: {
        strategyNotSelected: 'Strategija ni izbrana',
      },
      installPlugin: {
        desc: 'Namestitev naslednjega vtičnika',
        title: 'Namestite vtičnik',
        changelog: 'Zapis sprememb',
        cancel: 'Prekliči',
        install: 'Namestite',
      },
      toolbox: 'delovna orodja',
      configureModel: 'Konfiguriraj Model',
      toolNotInstallTooltip: '{{tool}} ni nameščen',
      pluginNotInstalled: 'Ta vtičnik ni nameščen',
      strategyNotInstallTooltip: '{{strategy}} ni nameščen',
      modelNotInstallTooltip: 'Ta model ni nameščen.',
      model: 'model',
      maxIterations: 'Maksimalne iteracije',
      notAuthorized: 'Nimam dovoljenja',
      modelNotSelected: 'Model ni izbran',
      learnMore: 'Nauči se več',
      unsupportedStrategy: 'Nepodprta strategija',
      pluginNotFoundDesc: 'Ta vtičnik je nameščen iz GitHuba. Prosimo, da greste v vtičnike in ga ponovo namestite.',
      tools: 'Orodja',
      strategyNotFoundDesc: 'V različici vtičnika, ki je nameščena, ta strategija ni zagotovljena.',
      linkToPlugin: 'Povezava do vtičnikov',
      strategyNotSet: 'Agentična strategija ni nastavljena',
      toolNotAuthorizedTooltip: '{{tool}} Ni pooblaščen',
      strategyNotFoundDescAndSwitchVersion: 'Nameščena različica vtičnika ne podpira te strategije. Kliknite za preklop na drugo različico.',
      pluginNotInstalledDesc: 'Ta vtičnik je nameščen iz GitHuba. Prosimo, da greste v vtičnike in ga ponovo namestite.',
    },
  },
  tracing: {
    stopBy: 'Ohranjaj se pri {{user}}',
  },
  versionHistory: {
    filter: {
      all: 'Vse',
      reset: 'Ponastavi filter',
      onlyShowNamedVersions: 'Prikaži samo poimenovane različice',
      onlyYours: 'Samo tvoje',
      empty: 'Ni najdene zgodovine različic, ki bi se ujemala.',
    },
    editField: {
      title: 'Naslov',
      titleLengthLimit: 'Naslov ne sme presegati {{limit}} znakov',
      releaseNotesLengthLimit: 'Opombe o različici ne smejo presegati {{limit}} znakov.',
      releaseNotes: 'Opombe o izdaji',
    },
    action: {
      deleteSuccess: 'Različica izbrisana',
      deleteFailure: 'Brisanje različice ni uspelo',
      updateFailure: 'Posodobitev različice ni uspela',
      restoreSuccess: 'Obnovljena različica',
      restoreFailure: 'Obnavljanje različice ni uspelo',
      updateSuccess: 'Različica posodobljena',
    },
    defaultName: 'Nepodpisana različica',
    deletionTip: 'Izbris je nepovraten, prosim potrdite.',
    currentDraft: 'Trenutni osnutek',
    title: 'Različice',
    editVersionInfo: 'Uredi informacije o različici',
    latest: 'Najnovejši',
    nameThisVersion: 'Poimenujte to različico',
    releaseNotesPlaceholder: 'Opisujte, kaj se je spremenilo',
    restorationTip: 'Po obnovitvi različice bo trenutni osnutek prepisan.',
  },
  debug: {
    noData: {
      runThisNode: 'Zagon te vozlišča',
      description: 'Rezultati zadnjega zagona bodo prikazani tukaj',
    },
    variableInspect: {
      trigger: {
        stop: 'Ustavi se',
        normal: 'Inspiciranje spremenljivk',
        clear: 'Jasno',
        cached: 'Poglej shranjene spremenljivke',
        running: 'Shranjevanje statusa delovanja',
      },
      emptyLink: 'Nauči se več',
      chatNode: 'Pogovor',
      envNode: 'Okolje',
      systemNode: 'Sistem',
      view: 'Oglej si dnevnik',
      title: 'Inspiciranje spremenljivk',
      clearNode: 'Počisti predpomnjeno spremenljivko',
      clearAll: 'Ponastavi vse',
      reset: 'Ponastavi na zadnjo vrednost izvajanja',
      edited: 'Uredjeno',
      resetConversationVar: 'Ponastavi spremenljivko pogovora na privzeto vrednost',
      emptyTip: 'Po prehodu skozi vozlišče na platnu ali po zagonu vozlišča korak za korakom lahko v pregledu spremenljivk vidite trenutno vrednost spremenljivke vozlišča.',
    },
    settingsTab: 'Nastavitve',
    lastRunTab: 'Zadnji zagon',
  },
}

export default translation
