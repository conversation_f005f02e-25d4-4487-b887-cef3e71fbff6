import requests
import json

if __name__ == "__main__":

    # 更新URL，添加前缀 "/v1/sandbox"
    url = "http://172.30.196.248:8194/v1/sandbox/run"  # 更新为正确的路径

    # 替换为你配置中的正确 API Key
    api_key = "dify-sandbox"  # 这里的值需要和服务器配置中的 config.App.Key 一致

    # 定义请求的负载
    payload = {
        "language": "python3",
        "code": "print(8149 * 2)",  # 测试代码
        "preload": ""  # 可选，填充需要的预加载代码
    }

    # 请求头添加 X-Api-Key
    headers = {
        "X-Api-Key": api_key  # 将此处的 api_key 替换为你的服务器配置中的正确值
    }

    # 发送POST请求
    response = requests.post(url, json=payload, headers=headers)

    # 检查响应状态并输出结果
    if response.status_code == 200:
        print("请求成功！")
        print("响应内容：")
        print(response.json())  # 假设服务器返回的是JSON响应
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print("错误信息:", response.text)
