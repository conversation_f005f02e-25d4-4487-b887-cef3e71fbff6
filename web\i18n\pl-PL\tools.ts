const translation = {
  title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  createCustomTool: 'Utwórz niestandardowe narzędzie',
  type: {
    all: 'Wszystkie',
    builtIn: 'Wbudowane',
    custom: 'Niestandardowe',
    workflow: 'Przep<PERSON>yw pracy',
  },
  contribute: {
    line1: 'Interesuje mnie ',
    line2: 'współtworzenie narzędzi dla Dify.',
    viewGuide: 'Zobacz przewodnik',
  },
  author: '<PERSON><PERSON><PERSON>',
  auth: {
    authorized: 'Zautoryzowane',
    setup: 'Skonfiguruj autoryzację aby użyć',
    setupModalTitle: 'Konfiguruj autoryzację',
    setupModalTitleDescription:
      'Po skonfigurowaniu poświadczeń wszyscy członkowie w przestrzeni roboczej mogą używać tego narzędzia podczas projektowania aplikacji.',
  },
  includeToolNum: '{{num}} narzęd<PERSON> zawa<PERSON>',
  addTool: '<PERSON><PERSON><PERSON> narzę<PERSON>',
  createTool: {
    title: 'Utw<PERSON>rz niestandardowe narzędzie',
    editAction: 'Konfiguruj',
    editTitle: 'Edytuj niestandardowe narzędzie',
    name: 'Nazwa',
    toolNamePlaceHolder: 'Wprowadź nazwę narzędzia',
    schema: 'Schemat',
    schemaPlaceHolder: 'Wprowadź tutaj swój schemat OpenAPI',
    viewSchemaSpec: 'Zobacz specyfikację OpenAPI-Swagger',
    importFromUrl: 'Importuj z adresu URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Proszę podać prawidłowy URL',
    examples: 'Przykłady',
    exampleOptions: {
      json: 'Pogoda (JSON)',
      yaml: 'Sklep Zoologiczny (YAML)',
      blankTemplate: 'Pusty szablon',
    },
    availableTools: {
      title: 'Dostępne narzędzia',
      name: 'Nazwa',
      description: 'Opis',
      method: 'Metoda',
      path: 'Ścieżka',
      action: 'Akcje',
      test: 'Test',
    },
    authMethod: {
      title: 'Metoda autoryzacji',
      type: 'Typ autoryzacji',
      keyTooltip:
        'Klucz nagłówka HTTP, Możesz pozostawić go z "Autoryzacja" jeśli nie wiesz co to jest lub ustaw go na niestandardową wartość',
      types: {
        none: 'Brak',
        api_key: 'Klucz API',
        apiKeyPlaceholder: 'Nazwa nagłówka HTTP dla Klucza API',
        apiValuePlaceholder: 'Wprowadź Klucz API',
      },
      key: 'Klucz',
      value: 'Wartość',
    },
    authHeaderPrefix: {
      title: 'Typ autoryzacji',
      types: {
        basic: 'Podstawowa',
        bearer: 'Bearer',
        custom: 'Niestandardowa',
      },
    },
    privacyPolicy: 'Polityka prywatności',
    privacyPolicyPlaceholder: 'Proszę wprowadzić politykę prywatności',
    customDisclaimer: 'Oświadczenie niestandardowe',
    customDisclaimerPlaceholder: 'Proszę wprowadzić oświadczenie niestandardowe',
    deleteToolConfirmTitle: 'Skasuj ten przyrząd?',
    deleteToolConfirmContent: 'Usunięcie narzędzia jest nieodwracalne. Użytkownicy nie będą mieli już dostępu do Twojego narzędzia.',
    toolInput: {
      name: 'Nazwa',
      required: 'Wymagane',
      descriptionPlaceholder: 'Opis znaczenia parametru',
      methodParameter: 'Parametr',
      label: 'Tagi',
      methodSetting: 'Ustawienie',
      description: 'Opis',
      method: 'Metoda',
      methodParameterTip: 'LLM wypełnia się podczas wnioskowania',
      labelPlaceholder: 'Wybierz tagi (opcjonalnie)',
      methodSettingTip: 'Użytkownik wypełnia konfigurację narzędzia',
      title: 'Wprowadzanie narzędzi',
    },
    nameForToolCall: 'Nazwa wywołania narzędzia',
    description: 'Opis',
    descriptionPlaceholder: 'Krótki opis przeznaczenia narzędzia, np. zmierz temperaturę dla konkretnej lokalizacji.',
    nameForToolCallTip: 'Obsługuje tylko cyfry, litery i podkreślenia.',
    nameForToolCallPlaceHolder: 'Służy do rozpoznawania maszyn, takich jak getCurrentWeather, list_pets',
    confirmTip: 'Będzie to miało wpływ na aplikacje korzystające z tego narzędzia',
    confirmTitle: 'Potwierdź, aby zapisać ?',
  },
  test: {
    title: 'Test',
    parametersValue: 'Parametry i Wartość',
    parameters: 'Parametry',
    value: 'Wartość',
    testResult: 'Wyniki testu',
    testResultPlaceholder: 'Wynik testu pojawi się tutaj',
  },
  thought: {
    using: 'Używanie',
    used: 'Użyty',
    requestTitle: 'Żądanie do',
    responseTitle: 'Odpowiedź od',
  },
  setBuiltInTools: {
    info: 'Informacje',
    setting: 'Ustawienia',
    toolDescription: 'Opis narzędzia',
    parameters: 'parametry',
    string: 'ciąg znaków',
    number: 'liczba',
    required: 'Wymagane',
    infoAndSetting: 'Informacje i Ustawienia',
    file: 'plik',
  },
  noCustomTool: {
    title: 'Brak niestandardowych narzędzi!',
    content:
      'Dodaj i zarządzaj niestandardowymi narzędziami tutaj, aby budować aplikacje AI.',
    createTool: 'Utwórz Narzędzie',
  },
  noSearchRes: {
    title: 'Przykro nam, brak wyników!',
    content:
      'Nie znaleźliśmy żadnych narzędzi pasujących do Twojego wyszukiwania.',
    reset: 'Resetuj Wyszukiwanie',
  },
  builtInPromptTitle: 'Komunikat',
  toolRemoved: 'Narzędzie usunięte',
  notAuthorized: 'Narzędzie nieautoryzowane',
  howToGet: 'Jak uzyskać',
  addToolModal: {
    manageInTools: 'Zarządzanie w Narzędziach',
    added: 'Dodane',
    type: 'typ',
    category: 'kategoria',
    add: 'dodawać',
    emptyTitle: 'Brak dostępnego narzędzia do przepływu pracy',
    emptyTip: 'Przejdź do "Przepływ pracy -> Opublikuj jako narzędzie"',
    emptyTitleCustom: 'Brak dostępnego narzędzia niestandardowego',
    emptyTipCustom: 'Tworzenie narzędzia niestandardowego',
  },
  openInStudio: 'Otwieranie w Studio',
  customToolTip: 'Dowiedz się więcej o niestandardowych narzędziach Dify',
  toolNameUsageTip: 'Nazwa wywołania narzędzia do wnioskowania i podpowiadania agentowi',
  noTools: 'Nie znaleziono narzędzi',
  copyToolName: 'Kopiuj nazwę',
}

export default translation
