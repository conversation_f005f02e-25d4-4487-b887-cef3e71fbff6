const translation = {
  createApp: 'アプリを作成する',
  types: {
    all: '全て',
    chatbot: 'チャットボット',
    agent: 'エージェント',
    workflow: 'ワークフロー',
    completion: 'テキスト生成',
    basic: '基本的な',
    advanced: 'チャットフロー',
  },
  duplicate: '複製',
  mermaid: {
    handDrawn: '手描き',
    classic: 'クラシック',
  },
  duplicateTitle: 'アプリを複製する',
  export: 'DSL をエクスポート',
  exportFailed: 'DSL のエクスポートに失敗しました。',
  importDSL: 'DSL ファイルをインポート',
  createFromConfigFile: 'DSL ファイルから作成する',
  importFromDSL: 'DSL からインポート',
  importFromDSLFile: 'DSL ファイルから',
  importFromDSLUrl: 'URL から',
  importFromDSLUrlPlaceholder: 'DSL リンクをここに貼り付けます',
  deleteAppConfirmTitle: 'このアプリを削除しますか？',
  deleteAppConfirmContent:
    'アプリを削除すると、元に戻すことはできません。他のユーザーはもはやこのアプリにアクセスできず、すべてのプロンプトの設定とログが永久に削除されます。',
  appDeleted: 'アプリが削除されました',
  appDeleteFailed: 'アプリの削除に失敗しました',
  join: 'コミュニティに参加する',
  communityIntro: 'さまざまなチャンネルでチームメンバーや貢献者、開発者と議論します。',
  roadmap: 'ロードマップを見る',
  newApp: {
    startFromBlank: '最初から作成',
    startFromTemplate: 'テンプレートから作成',
    captionAppType: 'どのタイプのアプリを作成しますか？',
    chatbotDescription: 'チャット形式のアプリケーションを構築します。このアプリは質問と回答の形式を使用し、複数のラウンドの継続的な会話を可能にします。',
    completionDescription: 'プロンプトに基づいて高品質のテキストを生成するアプリケーションを構築します。記事、要約、翻訳などを生成します。',
    completionWarning: 'この種類のアプリはもうサポートされなくなります。',
    agentDescription: 'タスクを自動的に完了するためのツールを選択できるインテリジェント エージェントを構築します',
    workflowDescription: '高度なカスタマイズが可能なワークフローに基づいて高品質のテキストを生成するアプリケーションを構築します。経験豊富なユーザー向けです。',
    workflowWarning: '現在ベータ版です',
    chatbotType: 'チャットボットのオーケストレーション方法',
    basic: '基本',
    basicTip: '初心者向け。後で「チャットフロー」に切り替えることができます',
    basicFor: '初心者向け',
    basicDescription: '基本オーケストレートは、組み込みのプロンプトを変更する機能がなく、簡単な設定を使用してチャットボット アプリをオーケストレートします。初心者向けです。',
    advanced: 'チャットフロー',
    advancedFor: '上級ユーザー向け',
    advancedDescription: 'ワークフロー オーケストレートは、ワークフロー形式でチャットボットをオーケストレートし、組み込みのプロンプトを編集する機能を含む高度なカスタマイズを提供します。経験豊富なユーザー向けです。',
    captionName: 'アプリのアイコンと名前',
    appNamePlaceholder: 'アプリ名を入力してください',
    captionDescription: '説明',
    appDescriptionPlaceholder: 'アプリの説明を入力してください',
    useTemplate: 'このテンプレートを使用する',
    previewDemo: 'デモをプレビュー',
    chatApp: 'アシスタント',
    chatAppIntro:
      'チャット形式のアプリケーションを構築したい。このアプリは質問と回答の形式を使用し、複数のラウンドの継続的な会話を可能にします。',
    agentAssistant: '新しいエージェント アシスタント',
    completeApp: 'テキスト ジェネレーター',
    completeAppIntro:
      'プロンプトに基づいて高品質のテキストを生成するアプリケーションを作成したい。記事、要約、翻訳などを生成します。',
    showTemplates: 'テンプレートから選択したい',
    hideTemplates: 'モード選択に戻る',
    Create: '作成する',
    Cancel: 'キャンセル',
    nameNotEmpty: '名前を入力してください',
    appTemplateNotSelected: 'テンプレートを選択してください',
    appTypeRequired: 'アプリの種類を選択してください',
    appCreated: 'アプリが作成されました',
    appCreateFailed: 'アプリの作成に失敗しました',
    Confirm: '確認する',
    caution: '注意',
    appCreateDSLErrorPart2: '続行しますか？',
    appCreateDSLErrorPart4: 'システムがサポートする DSL バージョン：',
    appCreateDSLErrorPart3: '現在のアプリケーションの DSL バージョン：',
    appCreateDSLErrorTitle: 'バージョンの非互換性',
    appCreateDSLWarning: '注意:DSL のバージョンの違いは、特定の機能に影響を与える可能性があります',
    appCreateDSLErrorPart1: 'DSL バージョンに大きな違いが検出されました。インポートを強制すると、アプリケーションが誤動作する可能性があります。',
    optional: '随意',
    forBeginners: '初心者向けの基本的なアプリタイプ',
    noTemplateFoundTip: '別のキーワードを使用して検索してみてください。',
    agentShortDescription: '推論と自律的なツールの使用を備えたインテリジェントエージェント',
    foundResults: '{{カウント}}業績',
    noTemplateFound: 'テンプレートが見つかりません',
    noAppsFound: 'アプリが見つかりませんでした',
    workflowShortDescription: 'インテリジェントな自動化のためのエージェントフロー',
    completionShortDescription: '複数ターンチャット向けに強化されたワークフロー',
    advancedUserDescription: '追加のメモリ機能とチャットボットインターフェースを備えたワークフロー',
    advancedShortDescription: 'メモリを使用した複雑なマルチターン対話のワークフロー',
    agentUserDescription: 'タスクの目標を達成するために反復的な推論と自律的なツールを使用できるインテリジェントエージェント。',
    foundResult: '{{カウント}}結果',
    forAdvanced: '上級ユーザー向け',
    chooseAppType: 'アプリタイプを選択',
    learnMore: '詳細情報',
    noIdeaTip: 'アイデアがありませんか？テンプレートをご覧ください',
    chatbotShortDescription: '簡単なセットアップの LLM ベースのチャットボット',
    chatbotUserDescription: '簡単な設定で LLM ベースのチャットボットを迅速に構築します。Chatflow は後で切り替えることができます。',
    workflowUserDescription: 'ドラッグ＆ドロップの簡易性で自律型 AI ワークフローを視覚的に構築',
    completionUserDescription: '簡単な構成でテキスト生成タスク用の AI アシスタントをすばやく構築します。',
    dropDSLToCreateApp: 'アプリを作成するにはここにDSLファイルをドロップしてください',
  },
  editApp: '情報を編集する',
  editAppTitle: 'アプリ情報を編集する',
  editDone: 'アプリ情報が更新されました',
  editFailed: 'アプリ情報の更新に失敗しました',
  iconPicker: {
    ok: 'OK',
    cancel: 'キャンセル',
    emoji: '絵文字',
    image: '画像',
  },
  switch: 'ワークフロー オーケストレートに切り替える',
  switchTipStart: '新しいアプリのコピーが作成され、新しいコピーがワークフロー オーケストレートに切り替わります。新しいコピーは ',
  switchTip: '切り替えを許可しません',
  switchTipEnd: ' 基本的なオーケストレートに戻ることはできません。',
  switchLabel: '作成されるアプリのコピー',
  removeOriginal: '元のアプリを削除する',
  switchStart: '切り替えを開始する',
  openInExplore: '"探索" で開く',
  typeSelector: {
    all: 'すべてのタイプ',
    chatbot: 'チャットボット',
    agent: 'エージェント',
    workflow: 'ワークフロー',
    completion: 'テキスト生成',
    advanced: 'チャットフロー',
  },
  tracing: {
    title: 'アプリのパフォーマンスの追跡',
    description: 'サードパーティの LLMOps サービスとトレースアプリケーションのパフォーマンス設定を行います。',
    config: '設定',
    view: '見る',
    collapse: '折りたたむ',
    expand: '展開',
    tracing: '追跡',
    disabled: '無効しました',
    disabledTip: 'まずはサービスの設定から始めましょう。',
    enabled: '有効しました',
    tracingDescription: 'LLM の呼び出し、コンテキスト、プロンプト、HTTP リクエストなど、アプリケーション実行の全ての文脈をサードパーティのトレースプラットフォームで取り込みます。',
    configProviderTitle: {
      configured: '設定しました',
      notConfigured: 'トレース機能を有効化するためには、サービスの設定が必要です。',
      moreProvider: 'その他のプロバイダー',
    },
    arize: {
      title: 'Arize',
      description: 'エンタープライズグレードのLLM可観測性、オンラインおよびオフライン評価、モニタリング、実験—OpenTelemetryによって支えられています。LLMおよびエージェント駆動型アプリケーション向けに特別に設計されています。',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'オープンソースおよびOpenTelemetryベースの可観測性、評価、プロンプトエンジニアリング、実験プラットフォームで、LLMワークフローおよびエージェントに対応します。',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'LLM を利用したアプリケーションのライフサイクル全段階を支援する、オールインワンの開発者向けプラットフォームです。',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'トレース、評価、プロンプトの管理、そしてメトリクスを駆使して、LLM アプリケーションのデバッグや改善に役立てます。',
    },
    opik: {
      title: 'オピック',
      description: 'Opik は、LLM アプリケーションを評価、テスト、監視するためのオープンソース プラットフォームです。',
    },
    inUse: '使用中',
    configProvider: {
      title: '配置 ',
      placeholder: '{{key}}を入力してください',
      project: 'プロジェクト',
      publicKey: '公開キー',
      secretKey: '秘密キー',
      viewDocsLink: '{{key}}に関するドキュメントを見る',
      removeConfirmTitle: '{{key}}の設定を削除しますか？',
      removeConfirmContent: '現在の設定は使用中です。これを削除すると、トレース機能が無効になります。',
    },
    weave: {
      title: '織る',
      description: 'Weave は、LLM アプリケーションを評価、テスト、および監視するためのオープンソースプラットフォームです。',
    },
  },
  answerIcon: {
    title: 'Web アプリアイコンを使用して🤖を置き換える',
    description: '共有アプリケーションの中で Web アプリアイコンを使用して🤖を置き換えるかどうか',
    descriptionInExplore: 'Explore で Web アプリアイコンを使用して🤖を置き換えるかどうか',
  },
  newAppFromTemplate: {
    sidebar: {
      Agent: 'エージェント',
      Programming: 'プログラミング',
      HR: '人事',
      Writing: 'ライティング',
      Recommended: '推奨',
      Workflow: 'ワークフロー',
      Assistant: '助手',
    },
    byCategories: 'カテゴリ別',
    searchAllTemplate: 'すべてのテンプレートを検索...',
  },
  showMyCreatedAppsOnly: '自分が作成したアプリ',
  appSelector: {
    label: 'アプリ',
    params: 'アプリパラメータ',
    noParams: 'パラメータは必要ありません',
    placeholder: 'アプリを選択...',
  },
  structOutput: {
    moreFillTip: '最大 10 レベルのネストを表示します',
    required: '必須',
    LLMResponse: 'LLM のレスポンス',
    configure: '設定',
    notConfiguredTip: '構造化出力が未設定です',
    structured: '構造化出力',
    structuredTip: '構造化出力は、モデルが常に指定された JSON スキーマに準拠した応答を生成することを保証する機能です。',
    modelNotSupported: 'モデルが対応していません',
    modelNotSupportedTip: '現在のモデルはこの機能に対応しておらず、自動的にプロンプトインジェクションに切り替わります。',
  },
  accessControl: 'Web アプリアクセス制御',
  accessItemsDescription: {
    anyone: '誰でもこの web アプリにアクセスできます（ログイン不要）',
    specific: '特定のプラットフォーム内メンバーのみがこの Web アプリにアクセスできます',
    organization: 'プラットフォーム内の全メンバーがこの Web アプリにアクセスできます',
    external: '認証済みの外部ユーザーのみがこの Web アプリにアクセスできます',
  },
  accessControlDialog: {
    title: 'アクセス権限',
    description: 'Web アプリのアクセス権限を設定します',
    accessLabel: '誰がアクセスできますか',
    accessItems: {
      anyone: 'リンクを知っているすべてのユーザー',
      specific: '特定のプラットフォーム内メンバー',
      organization: 'プラットフォーム内の全メンバー',
      external: '認証済みの外部ユーザー',
    },
    groups_one: '{{count}} グループ',
    groups_other: '{{count}} グループ',
    members_one: '{{count}} メンバー',
    members_other: '{{count}} メンバー',
    noGroupsOrMembers: 'グループまたはメンバーが選択されていません',
    webAppSSONotEnabledTip: 'Web アプリの外部認証方式を設定するには、組織の管理者にお問い合わせください。',
    operateGroupAndMember: {
      searchPlaceholder: 'グループやメンバーを検索',
      allMembers: 'すべてのメンバー',
      expand: '展開',
      noResult: '結果がありません',
    },
    updateSuccess: '更新が成功しました',
  },
  publishApp: {
    title: 'Web アプリへのアクセス権',
    notSet: '未設定',
    notSetDesc: '現在この Web アプリには誰もアクセスできません。権限を設定してください。',
  },
  noAccessPermission: 'Web アプリにアクセス権限がありません',
}

export default translation
